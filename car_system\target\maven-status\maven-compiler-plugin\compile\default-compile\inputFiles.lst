C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\entity\LaborPayment.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\entity\Technician.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\controller\MaterialController.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\repository\OrderStatusHistoryRepository.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\VehicleRepairManagementApplication.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\controller\AuthController.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\repository\MaterialRepository.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\repository\OrderMaterialUsageRepository.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\entity\User.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\LaborPaymentDTO.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\request\MaterialUsageRequest.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\TechnicianPerformanceDTO.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\entity\OrderFeedback.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\repository\OrderUrgentRequestRepository.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\controller\UserController.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\entity\RepairOrder.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\validation\FutureTimeValidator.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\repository\TechnicianRepository.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\service\MaterialService.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\service\AuthService.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\config\AppProperties.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\request\UserUpdateRequest.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\controller\FaultTypeController.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\request\OrderCompletionRequest.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\service\AnalyticsService.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\service\TestDataInitializer.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\repository\FaultTypeRepository.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\config\JwtProperties.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\entity\OrderUrgentRequest.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\WorkloadStatisticsDTO.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\repository\UserRepository.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\service\OrderService.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\VehicleDTO.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\request\OrderUrgentRequest.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\entity\FaultType.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\exception\GlobalExceptionHandler.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\repository\OrderTechnicianAssignmentRepository.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\FeedbackDTO.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\security\SecurityConfig.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\entity\BaseEntity.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\request\FaultTypeRequest.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\repository\LaborPaymentRepository.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\ApiResponse.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\service\DataInitializationService.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\request\OrderStatusUpdateRequest.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\request\TechnicianRegistrationRequest.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\LoginResponse.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\controller\OrderController.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\repository\VehicleRepository.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\SpecialtyDTO.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\security\JwtAuthenticationFilter.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\MaterialUsageDTO.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\UrgentRequestDTO.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\service\TechnicianService.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\controller\AnalyticsController.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\entity\OrderTechnicianAssignment.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\exception\ResourceNotFoundException.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\RepairStatisticsDTO.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\security\JwtUtil.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\request\MaterialRequest.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\request\OrderFeedbackRequest.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\service\AdminService.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\exception\BusinessException.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\MaterialDTO.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\entity\Vehicle.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\service\TestDataHelper.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\request\OrderRequest.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\UserDTO.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\OrderDTO.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\service\FaultTypeService.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\controller\TechnicianController.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\CostAnalysisDTO.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\FaultTypeDTO.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\repository\OrderFeedbackRepository.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\repository\RepairOrderRepository.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\controller\VehicleController.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\validation\FutureTime.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\PatternStatisticsDTO.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\entity\OrderMaterialUsage.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\entity\Material.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\request\TechnicianUpdateRequest.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\request\VehicleRequest.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\controller\AdminController.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\service\UserService.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\request\UserRegistrationRequest.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\TechnicianDTO.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\entity\OrderStatusHistory.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\security\JwtAuthenticationEntryPoint.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\request\LoginRequest.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\dto\response\PageResponse.java
C:\Users\<USER>\Desktop\db_pj\car_system\src\main\java\com\example\service\VehicleService.java
